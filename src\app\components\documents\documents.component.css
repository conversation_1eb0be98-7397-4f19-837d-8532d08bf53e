/* Document list styles */
.card {
  @apply bg-card rounded-lg border shadow-sm;
}

/* Table hover effect */
tbody tr {
  transition: background-color 0.2s ease;
}

/* File icon styles */
.file-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: #f3f4f6;
}

/* Category button styles */
.category-button {
  transition: all 0.2s ease;
}

.category-button:hover {
  background-color: #f3f4f6;
}

.category-button.active {
  background-color: #ebf5ff;
  color: #3b82f6;
  font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}
