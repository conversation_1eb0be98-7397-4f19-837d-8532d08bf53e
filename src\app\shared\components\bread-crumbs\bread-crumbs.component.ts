import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, NavigationEnd, ActivatedRoute, RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { filter } from 'rxjs/operators';

interface Breadcrumb {
  label: string;
  url: string;
}

@Component({
  selector: 'app-bread-crumbs',
  standalone: true,
  imports: [CommonModule, RouterModule, TranslateModule],
  templateUrl: './bread-crumbs.component.html',
  styleUrls: ['./bread-crumbs.component.css']
})
export class BreadCrumbsComponent implements OnInit {
  breadcrumbs: Breadcrumb[] = [];

  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe(() => {
        this.breadcrumbs = this.createBreadcrumbs(this.activatedRoute.root);
      });

    // Initialize breadcrumbs
    this.breadcrumbs = this.createBreadcrumbs(this.activatedRoute.root);
  }

  private createBreadcrumbs(route: ActivatedRoute, url: string = '', breadcrumbs: Breadcrumb[] = []): Breadcrumb[] {
    // Get the route children
    const children: ActivatedRoute[] = route.children;

    // Return if there are no more children
    if (children.length === 0) {
      return breadcrumbs;
    }

    // For each child
    for (const child of children) {
      // Get the route's URL segment
      const routeURL: string = child.snapshot.url.map(segment => segment.path).join('/');
      
      // Append route URL to URL
      if (routeURL !== '') {
        url += `/${routeURL}`;
      }

      // Add breadcrumb
      if (child.snapshot.data['breadcrumb']) {
        breadcrumbs.push({
          label: child.snapshot.data['breadcrumb'],
          url: url
        });
      } else {
        // Handle specific routes manually
        this.addManualBreadcrumb(url, breadcrumbs);
      }

      // Recursive
      return this.createBreadcrumbs(child, url, breadcrumbs);
    }

    return breadcrumbs;
  }

  private addManualBreadcrumb(url: string, breadcrumbs: Breadcrumb[]): void {
    // Handle specific routes manually
    if (url === '') {
      breadcrumbs.push({
        label: 'BREADCRUMB.HOME',
        url: '/'
      });
    } else if (url === '/courses') {
      breadcrumbs.push({
        label: 'BREADCRUMB.COURSES',
        url: '/courses'
      });
    } else if (url.includes('/thread/')) {
      breadcrumbs.push({
        label: 'BREADCRUMB.COURSES',
        url: '/courses'
      });
      breadcrumbs.push({
        label: 'BREADCRUMB.COURSE_DETAILS',
        url: url
      });
    } else if (url === '/news') {
      breadcrumbs.push({
        label: 'BREADCRUMB.NEWS',
        url: '/news'
      });
    } else if (url.includes('/news/')) {
      breadcrumbs.push({
        label: 'BREADCRUMB.NEWS',
        url: '/news'
      });
      breadcrumbs.push({
        label: 'BREADCRUMB.NEWS_DETAILS',
        url: url
      });
    } else if (url === '/tasks') {
      breadcrumbs.push({
        label: 'BREADCRUMB.TASKS',
        url: '/tasks'
      });
    } else if (url === '/schedule') {
      breadcrumbs.push({
        label: 'BREADCRUMB.SCHEDULE',
        url: '/schedule'
      });
    } else if (url === '/ratings') {
      breadcrumbs.push({
        label: 'BREADCRUMB.RATINGS',
        url: '/ratings'
      });
    } else if (url === '/profile') {
      breadcrumbs.push({
        label: 'BREADCRUMB.PROFILE',
        url: '/profile'
      });
    } else if (url === '/register') {
      breadcrumbs.push({
        label: 'BREADCRUMB.REGISTRATION',
        url: '/register'
      });
    }
  }
}
