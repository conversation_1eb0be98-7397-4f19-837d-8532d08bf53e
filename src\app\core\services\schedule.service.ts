import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, catchError, of } from 'rxjs';
import { environments } from '../../environments/environments';
import { AuthService } from './auth.service';
import { StorageService } from './storage.service';

@Injectable({
  providedIn: 'root'
})
export class ScheduleService {
  private apiUrl = environments.API;

  constructor(
    private http: HttpClient,
    private authService: AuthService,
    private storageService: StorageService
  ) {}

  /**
   * Get student schedule for the current week
   * @returns Observable with schedule details
   */
  getStudentSchedule(): Observable<any> {
    // Get the current user from the auth service
    const currentUser = this.authService.getCurrentUser();

    if (!currentUser || !currentUser.id) {
      throw new Error('User not authenticated');
    }

    // Check if user is a student
    if (currentUser.role !== 'student') {
      throw new Error('Only students can access their schedule');
    }

    return this.http.get<any>(
      `${this.apiUrl}/students/${currentUser.id}/schedule`
    ).pipe(
      // Handle empty responses or errors gracefully
      catchError(error => {
        console.error('Error fetching schedule:', error);
        return of({ student_id: currentUser.id, schedules: [] });
      })
    );
  }

  /**
   * Get student schedule for a specific date range
   * @param startDate Start date in ISO format
   * @param endDate End date in ISO format
   * @returns Observable with schedule details
   */
  getStudentScheduleByDateRange(startDate: string, endDate: string): Observable<any> {
    // Get the current user from the auth service
    const currentUser = this.authService.getCurrentUser();

    if (!currentUser || !currentUser.id) {
      throw new Error('User not authenticated');
    }

    // Check if user is a student
    if (currentUser.role !== 'student') {
      throw new Error('Only students can access their schedule');
    }

    return this.http.get<any>(
      `${this.apiUrl}/students/${currentUser.id}/schedule`,
      { params: { start_date: startDate, end_date: endDate } }
    ).pipe(
      // Handle empty responses or errors gracefully
      catchError(error => {
        console.error('Error fetching schedule:', error);
        return of({ student_id: currentUser.id, schedules: [] });
      })
    );
  }

  /**
   * Get teacher schedule using the my-threads endpoint
   * @returns Observable with teacher threads and schedules
   */
  getTeacherThreads(): Observable<any> {
    // Get the current user from the auth service
    const currentUser = this.authService.getCurrentUser();

    if (!currentUser || !currentUser.id) {
      throw new Error('User not authenticated');
    }

    // Check if user is a teacher
    if (currentUser.role !== 'teacher') {
      throw new Error('Only teachers can access their schedule');
    }

    // Get the token for authorization
    const token = this.storageService.getToken();
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);

    return this.http.get<any>(
      `${this.apiUrl}/teacher/my-threads`,
      { headers: headers }
    ).pipe(
      // Handle empty responses or errors gracefully
      catchError(error => {
        console.error('Error fetching teacher threads:', error);
        return of({ teacher: currentUser, threads: [] });
      })
    );
  }
}
