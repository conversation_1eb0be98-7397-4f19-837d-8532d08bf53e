<!-- Заголовок -->
<div class="text-xl text-gray-800 mt-5 px-5">Редактировать группу заданий</div>

<!-- Форма -->
<form [formGroup]="assignmentGroupForm" class="space-y-3 p-5 px-8">

  <!-- Название -->
  <div>
    <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Название</label>
    <input
      id="name"
      type="text"
      formControlName="name"
      class="w-full border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring focus:ring-blue-200"
    >
    <div *ngIf="assignmentGroupForm.get('name')?.invalid && assignmentGroupForm.get('name')?.touched" 
         class="text-xs text-red-500 mt-1">
      Название обязательно
    </div>
  </div>

  <!-- Тип -->
  <div>
    <label for="type" class="block text-sm font-medium text-gray-700 mb-1">Тип</label>
    <select
      id="type"
      formControlName="group_type"
      class="w-full border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring focus:ring-blue-200"
    >
      <option value="midterm">Midterm</option>
      <option value="endterm">Endterm</option>
      <option value="final">Final</option>
      <option value="custom">Custom</option>
    </select>
  </div>

  <!-- Вес -->
  <div>
    <label for="weight" class="block text-sm font-medium text-gray-700 mb-1">Вес (от 0.001 до 0.9)</label>
    <input
      id="weight"
      type="number"
      step="0.01"
      min="0.001"
      max="0.9"
      formControlName="weight"
      class="w-full border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring focus:ring-blue-200"
    >
    <div *ngIf="assignmentGroupForm.get('weight')?.invalid && assignmentGroupForm.get('weight')?.touched" 
         class="text-xs text-red-500 mt-1">
      Вес должен быть от 0.001 до 0.9
    </div>
  </div>

  <!-- Кнопки -->
  <div class="flex justify-end space-x-2 pt-3">
    <button
      type="button"
      (click)="cancel()"
      class="px-4 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-100 focus:outline-none focus:ring focus:ring-blue-200"
    >
      Отмена
    </button>
    <button
      type="submit"
      [disabled]="assignmentGroupForm.invalid"
      (click)="submit()"
      class="px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring focus:ring-blue-200 disabled:opacity-50"
    >
      Сохранить
    </button>
  </div>
</form>
