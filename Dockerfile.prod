# Stage 1: Сборка Angular-приложения
FROM node:18 AS build
WORKDIR /app

# Копируем файлы, необходимые для установки зависимостей
COPY package*.json ./

RUN rm -rf node_modules package-lock.json
# Устанавливаем зависимости
RUN npm install

# Копируем весь исходный код проекта
COPY . .

# Собираем приложение в production-режиме
RUN npm run build -- --configuration production

# Stage 2: Запуск через Nginx
FROM nginx:alpine

# Копируем собранную статику из Stage 1
COPY --from=build /app/dist/edunite_v2/browser /usr/share/nginx/html
COPY nginx/nginx.conf /etc/nginx/nginx.conf
EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
