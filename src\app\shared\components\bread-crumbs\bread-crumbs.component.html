<div class="hidden sm:flex items-center">
  <ng-container *ngFor="let breadcrumb of breadcrumbs; let last = last; let i = index"> 
    <ng-container *ngIf="i > 0"> 
      <span class="text-xs" [ngClass]="{'font-medium': last}">{{ breadcrumb.label | translate }}</span>
    </ng-container>
  </ng-container>
  
  <!-- Fallback if no breadcrumbs -->
  <ng-container *ngIf="breadcrumbs.length === 0">
    <span class="text-tertiary text-xs mr-2">{{ 'HEADER.DASHBOARDS' | translate }}</span>
    <span class="text-tertiary text-xs mx-2">/</span>
    <span class="text-xs font-medium">{{ 'HEADER.DEFAULT' | translate }}</span>
  </ng-container>
</div>
