﻿
module.exports = {
  darkMode: 'class',
  content: [
    "./src*.{html,ts}",
  ],
  theme: {
    fontFamily: {
      'sans': ['SF Pro', 'SF Pro Display', 'SF Pro Cyrillic Fallback', 'system-ui', 'Avenir', 'Helvetica', 'Arial', 'sans-serif'],
      'sf-pro': ['SF Pro', 'SF Pro Cyrillic Fallback', 'sans-serif'],
      'sf-pro-display': ['SF Pro Display', 'SF Pro Cyrillic Fallback', 'sans-serif']
    },
    extend: {
      colors: {
        primary: {
          50: 'var(--primary-50)',
          100: 'var(--primary-100)',
          200: 'var(--primary-200)',
          300: 'var(--primary-300)',
          400: 'var(--primary-400)',
          500: 'var(--primary-500)',
          600: 'var(--primary-600)',
          700: 'var(--primary-700)',
          800: 'var(--primary-800)',
          900: 'var(--primary-900)',
        },
                bg: {
          primary: 'var(--bg-primary)',
          secondary: 'var(--bg-secondary)',
          tertiary: 'var(--bg-tertiary)',
        },
        text: {
          primary: 'var(--text-primary)',
          secondary: 'var(--text-secondary)',
          tertiary: 'var(--text-tertiary)',
        }
      },
      backgroundColor: {
        primary: 'var(--bg-primary)',
        secondary: 'var(--bg-secondary)',
        tertiary: 'var(--bg-tertiary)',
        card: 'var(--card-bg)',
        input: 'var(--input-bg)',
      },
      textColor: {
        primary: 'var(--text-primary)',
        secondary: 'var(--text-secondary)',
        tertiary: 'var(--text-tertiary)',
      },
      borderColor: {
        DEFAULT: 'var(--border-color)',
        light: 'var(--border-color-light)',
        input: 'var(--input-border)',
        'input-focus': 'var(--input-focus-border)',
      },
      boxShadow: {
        'sm': 'var(--shadow-sm)',
        DEFAULT: 'var(--shadow)',
        'md': 'var(--shadow-md)',
        'lg': 'var(--shadow-lg)',
        'card': '0 2px 5px 0 rgba(0, 0, 0, 0.05)',
        'card-hover': '0 5px 15px 0 rgba(0, 0, 0, 0.07)',
      }
    },
  },
  plugins: [
      ],
}
