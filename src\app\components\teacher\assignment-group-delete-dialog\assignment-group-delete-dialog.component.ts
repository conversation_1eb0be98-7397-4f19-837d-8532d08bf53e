import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";

@Component({
  selector: 'app-assignment-group-delete-dialog',
  templateUrl: './assignment-group-delete-dialog.component.html',
  styleUrl: './assignment-group-delete-dialog.component.css'
})
export class AssignmentGroupDeleteDialogComponent {
  captchaInput: string = '';
  captchaText: string = 'УДАЛИТЬ';

  constructor(
    private dialogRef: MatDialogRef<AssignmentGroupDeleteDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { group: any }
  ) {}

  confirm(): void {
    if (this.captchaInput === this.captchaText) {
      this.dialogRef.close(true);
    }
  }

  cancel(): void {
    this.dialogRef.close(false);
  }
}
