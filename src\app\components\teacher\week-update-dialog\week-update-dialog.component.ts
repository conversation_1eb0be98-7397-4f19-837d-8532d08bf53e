import { Component, Inject, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from "@angular/forms";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";

@Component({
  selector: 'app-week-update-dialog',
  templateUrl: './week-update-dialog.component.html',
  styleUrl: './week-update-dialog.component.css'
})
export class WeekUpdateDialogComponent implements OnInit {
  weekForm: FormGroup;

  constructor(
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<WeekUpdateDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { week: any, threadId: number }
  ) {
    this.weekForm = this.fb.group({
      week_number: [null, [Validators.required, Validators.min(1)]],
      type: ['', Validators.required],
      title: ['', Validators.required],
      description: ['']
    });
  }

  ngOnInit(): void {
    // Initialize form with existing week data
    this.weekForm.patchValue({
      week_number: this.data.week.week_number,
      type: this.data.week.type,
      title: this.data.week.title,
      description: this.data.week.description
    });
  }

  submit() {
    if (this.weekForm.invalid) return;

    const formData = this.weekForm.value;

    this.dialogRef.close({
      ...formData,
      id: this.data.week.id,
      thread_id: this.data.threadId
    });
  }

  cancel() {
    this.dialogRef.close(); // Cancel
  }
}
