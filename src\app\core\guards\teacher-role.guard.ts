import {CanActivateFn, Router} from '@angular/router';
import {inject} from '@angular/core';
import {StorageService} from "../services/storage.service";
import {MatSnackBar} from "@angular/material/snack-bar";

export const teacherRoleGuard: CanActivateFn = (route, state) => {
  const storageService = inject(StorageService);
  const router = inject(Router);
  const snackBar = inject(MatSnackBar);

  const user = storageService.getUser();
  
  if (user && (user.role === 'teacher' || user.role === 'admin')) {
    return true;
  } else {
    snackBar.open('Доступ запрещен: Только преподаватели имеют доступ к этой странице', 'Закрыть', {
      duration: 5000,
      panelClass: ['snackbar-error'],
    });
    router.navigate(['/']);
    return false;
  }
};
