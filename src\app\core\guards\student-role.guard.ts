import {CanActivateFn, Router} from '@angular/router';
import {inject} from '@angular/core';
import {StorageService} from "../services/storage.service";
import {MatSnackBar} from "@angular/material/snack-bar";

export const studentRoleGuard: CanActivateFn = (route, state) => {
  const storageService = inject(StorageService);
  const router = inject(Router);
  const snackBar = inject(MatSnackBar);

  const user = storageService.getUser();
  
  if (user && user.role === 'student') {
    return true;
  } else {
    snackBar.open('Доступ запрещен: Только студенты имеют доступ к регистрации на курсы', 'Закрыть', {
      duration: 5000,
      panelClass: ['snackbar-error'],
    });
    router.navigate(['/']);
    return false;
  }
};
