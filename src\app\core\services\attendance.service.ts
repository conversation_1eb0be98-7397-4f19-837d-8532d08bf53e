import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { environments } from '../../environments/environments';
import { StorageService } from './storage.service';

export enum AttendanceStatus {
  UNSPECIFIED = 0,
  UNMARKED = 1,
  PRESENT = 2,
  ABSENT = 3,
  EXCUSED = 4
}

export interface AttendanceRecord {
  id?: number;
  thread_id: number;
  user_id: number;
  attendance_date: any; // Using any for now to handle the seconds format
  status: AttendanceStatus;
  reason?: string;
  created_at?: any;
  updated_at?: any;
}

@Injectable({
  providedIn: 'root'
})
export class AttendanceService {
  private apiUrl = environments.API;

  constructor(
    private http: HttpClient,
    private storage: StorageService
  ) { }

  private checkTeacherRole(): boolean {
    const user = this.storage.getUser();
    return user && (user.role === 'teacher' || user.role === 'admin');
  }

  /**
   * Get attendance records for a thread on a specific date
   * @param thread_id The thread ID
   * @param attendance_date The date in YYYY-MM-DD format
   */
  getAttendanceRecords(thread_id: number, attendance_date: string): Observable<AttendanceRecord[]> {
    const params = new HttpParams()
      .set('thread_id', thread_id.toString())
      .set('attendance_date', attendance_date);

    return this.http.get<AttendanceRecord[]>(`${this.apiUrl}/attendance`, { params });
  }

  /**
   * Create a new attendance record
   * @param record The attendance record to create
   */
  createAttendanceRecord(record: AttendanceRecord): Observable<AttendanceRecord> {
    if (!this.checkTeacherRole()) {
      return throwError(() => new Error('Unauthorized: Only teachers and admins can create attendance records'));
    }

    return this.http.post<AttendanceRecord>(`${this.apiUrl}/attendance`, record);
  }

  /**
   * Update an existing attendance record
   * @param id The attendance record ID
   * @param data The updated attendance data
   */
  updateAttendanceRecord(id: number, data: { status: AttendanceStatus, reason?: string }): Observable<AttendanceRecord> {
    if (!this.checkTeacherRole()) {
      return throwError(() => new Error('Unauthorized: Only teachers and admins can update attendance records'));
    }

    return this.http.put<AttendanceRecord>(`${this.apiUrl}/attendance/${id}`, data);
  }

  /**
   * Format date to YYYY-MM-DD format for API requests
   * @param date The date to format (can be Date object, string, or any other format)
   */
  formatDateForApi(date: any): string {
    try {
      // Ensure we have a valid Date object
      const dateObj = date instanceof Date ? date : new Date(date);

      // Check if the date is valid
      if (isNaN(dateObj.getTime())) {
        console.warn('Invalid date provided to formatDateForApi:', date);
        // Return today's date as fallback
        const today = new Date();
        return `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
      }

      const year = dateObj.getFullYear();
      const month = String(dateObj.getMonth() + 1).padStart(2, '0');
      const day = String(dateObj.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    } catch (error) {
      console.error('Error formatting date:', error);
      // Return today's date as fallback
      const today = new Date();
      return `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
    }
  }

  /**
   * Parse date from API response (seconds format)
   * @param dateObj The date object from API with seconds property
   */
  parseApiDate(dateObj: { seconds: number } | any): Date {
    try {
      if (!dateObj) {
        return new Date();
      }

      // Handle seconds format
      if (dateObj.seconds) {
        return new Date(dateObj.seconds * 1000);
      }

      // Handle string format
      if (typeof dateObj === 'string') {
        return new Date(dateObj);
      }

      // Handle timestamp format
      if (typeof dateObj === 'number') {
        return new Date(dateObj);
      }

      // Default to current date
      return new Date();
    } catch (error) {
      console.error('Error parsing API date:', error);
      return new Date();
    }
  }

  /**
   * Format date for display in UI (DD.MM format)
   * @param date The date to format
   */
  formatDateForDisplay(date: Date | any): string {
    try {
      // Ensure we have a valid Date object
      const dateObj = date instanceof Date ? date : new Date(date);

      // Check if the date is valid
      if (isNaN(dateObj.getTime())) {
        console.warn('Invalid date provided to formatDateForDisplay:', date);
        // Return empty string as fallback
        return '';
      }

      const day = String(dateObj.getDate()).padStart(2, '0');
      const month = String(dateObj.getMonth() + 1).padStart(2, '0');
      return `${day}.${month}`;
    } catch (error) {
      console.error('Error formatting date for display:', error);
      return '';
    }
  }
}
