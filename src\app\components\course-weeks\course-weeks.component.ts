import {Component, Input, OnChanges, OnInit, SimpleChanges} from '@angular/core';
import {ActivatedRoute, Router} from "@angular/router";
import {WeekService} from "../../core/services/week.service";
import {MatSnackBar} from "@angular/material/snack-bar";
import {AuthService} from "../../core/services/auth.service";

@Component({
  selector: 'app-course-weeks',
  templateUrl: './course-weeks.component.html',
  styleUrl: './course-weeks.component.css'
})
export class CourseWeeksComponent implements OnInit, OnChanges {

  @Input() user: any;
  @Input() threadID: any;

  constructor(private route: ActivatedRoute,
              private router: Router,
              private weekService: WeekService,
              private snackBar: MatSnackBar,
              private authService: AuthService,) {
    const user = authService.getCurrentUser()
  }

  // Search and filter properties
  searchTerm: string = '';
  selectedType: string = 'all'; // 'all', 'task', 'info'
  selectedStatus: string = 'all'; // 'all', 'completed', 'not_completed', 'under_review'
  selectedWeek: number | null = null;
  isLoading: boolean = false;
  expandedWeeks: Set<number> = new Set(); // Track expanded/collapsed weeks

  selectedWeekIdForDelete: number | null = null;
  showDeleteModal: boolean = false;
  captchaInput: string = '';

  homeworkData: any;
  weeks: any[] = [];
  filteredWeeks: any[] = [];

  ngOnInit() {
    console.log(this.user)
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (this.threadID && this.user?.id) {
      this.loadHomework(this.threadID, this.user.id);
      console.log('this.homeworkData'  , this.homeworkData )
    }
  }

  /**
   * Toggle week expansion/collapse
   */
  toggleWeekExpansion(weekId: number): void {
    if (this.expandedWeeks.has(weekId)) {
      this.expandedWeeks.delete(weekId);
    } else {
      this.expandedWeeks.add(weekId);
    }
  }

  /**
   * Check if a week is expanded
   */
  isWeekExpanded(weekId: number): boolean {
    return this.expandedWeeks.has(weekId);
  }

  /**
   * Apply filters and search to the weeks data
   */
  applyFilters(): void {
    if (!this.weeks || this.weeks.length === 0) {
      this.filteredWeeks = [];
      return;
    }

    // Start with a deep copy of the original weeks
    this.filteredWeeks = JSON.parse(JSON.stringify(this.weeks));

    // Filter by selected week if applicable
    if (this.selectedWeek !== null) {
      this.filteredWeeks = this.filteredWeeks.filter(week => week.id === this.selectedWeek);
    }

    // Filter assignments within each week
    this.filteredWeeks = this.filteredWeeks.map(week => {
      // Create a copy of the week
      const filteredWeek = { ...week };

      // Filter assignments if they exist
      if (filteredWeek.assignments && filteredWeek.assignments.length > 0) {
        filteredWeek.assignments = filteredWeek.assignments.filter((item: any) => {
          // Apply type filter
          if (this.selectedType !== 'all' && item.assignment.type !== this.selectedType) {
            return false;
          }

          // Apply status filter (only for students and tasks)
          if (this.user.role !== 'teacher' && item.assignment.type === 'task' && this.selectedStatus !== 'all') {
            if (this.selectedStatus === 'completed' && !item.submission) {
              return false;
            }
            if (this.selectedStatus === 'not_completed' && item.submission) {
              return false;
            }
            if (this.selectedStatus === 'under_review' &&
                (!item.submission || item.submission.score !== undefined)) {
              return false;
            }
          }

          // Apply search term filter
          if (this.searchTerm.trim() !== '') {
            const searchLower = this.searchTerm.toLowerCase().trim();
            return item.assignment.title.toLowerCase().includes(searchLower) ||
                  (item.assignment.description &&
                   item.assignment.description.toLowerCase().includes(searchLower));
          }

          return true;
        });
      }

      return filteredWeek;
    });

    // Remove weeks with no matching assignments
    this.filteredWeeks = this.filteredWeeks.filter(week =>
      week.assignments && week.assignments.length > 0
    );
  }

  /**
   * Reset all filters
   */
  resetFilters(): void {
    this.searchTerm = '';
    this.selectedType = 'all';
    this.selectedStatus = 'all';
    this.selectedWeek = null;
    this.applyFilters();
  }



  deleteWeekPrompt(weekID: number) {
    this.selectedWeekIdForDelete = weekID;
    this.showDeleteModal = true;
    this.captchaInput = '';
  }

  cancelDelete() {
    this.showDeleteModal = false;
    this.selectedWeekIdForDelete = null;
    this.captchaInput = '';
  }

  confirmDelete() {
    if (this.selectedWeekIdForDelete) {
      this.weekService.deleteWeek(this.selectedWeekIdForDelete).subscribe({
        next: () => {
          this.snackBar.open('Неделя удалена', 'Закрыть', { duration: 3000 });
          this.showDeleteModal = false;
          this.loadHomework(this.threadID, this.user.id);
        },
        error: () => {
          this.snackBar.open('Ошибка при удалении', 'Закрыть', { duration: 3000 });
        }
      });
    }
  }


  public loadHomework(threadId: number, userId: number) {
    this.isLoading = true;
    this.weekService.getThreadHomework(threadId, userId).subscribe({
      next: data => {
        this.homeworkData = data;

        const rawWeeks = data.weeks;

        // 🛡 Безопасная проверка
        if (Array.isArray(rawWeeks)) {
          this.weeks = rawWeeks.map((item: any) => ({
            ...item.week,
            assignments: item.assignments || []
          }));

          // Set all weeks to expanded by default
          this.weeks.forEach(week => {
            this.expandedWeeks.add(week.id);
          });

          // Initialize filtered weeks
          this.filteredWeeks = [...this.weeks];
        } else {
          this.weeks = [];  // если null или не массив — пустой список
          this.filteredWeeks = [];
        }

        console.log('WEEKS:', this.weeks);
        this.isLoading = false;
      },
      error: err => {
        console.error('Ошибка загрузки ДЗ', err);
        this.isLoading = false;
      }
    });
  }

  /**
   * Calculate the percentage score
   * @param score Current score
   * @param maxPoints Maximum possible points
   * @returns Percentage score (0-100)
   */
  getScorePercentage(score: number | undefined | null, maxPoints: number): number {
    if (score === undefined || score === null || maxPoints === 0) {
      return 0;
    }
    return (score / maxPoints) * 100;
  }

  /**
   * Determine the deadline status based on how close it is to the current date
   * @param dueDate The due date timestamp in seconds
   * @returns A string representing the deadline status: 'far', 'approaching', 'close', 'overdue', or 'none'
   */
  getDeadlineStatus(dueDate: any): string {
    if (!dueDate || !dueDate.seconds) {
      return 'none'; // No deadline
    }

    const now = new Date().getTime();
    const deadline = new Date(dueDate.seconds * 1000).getTime();
    const diffInDays = (deadline - now) / (1000 * 60 * 60 * 24);

    if (diffInDays < 0) {
      return 'overdue'; // Past deadline
    } else if (diffInDays < 1) {
      return 'close'; // Less than 1 day
    } else if (diffInDays < 3) {
      return 'approaching'; // Less than 3 days
    } else {
      return 'far'; // More than 3 days
    }
  }

}
