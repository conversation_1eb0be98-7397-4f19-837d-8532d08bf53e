/* San Francisco Pro Font Declarations */
@font-face {
  font-family: 'SF Pro';
  src: url('/assets/fonts/sf-pro/SF-Pro-Text-Regular.otf') format('opentype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: "kern", "liga", "calt";
}

@font-face {
  font-family: 'SF Pro';
  src: url('/assets/fonts/sf-pro/SF-Pro-Text-RegularItalic.otf') format('opentype');
  font-weight: 400;
  font-style: italic;
  font-display: swap;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: "kern", "liga", "calt";
}

@font-face {
  font-family: 'SF Pro';
  src: url('/assets/fonts/sf-pro/SF-Pro-Text-Light.otf') format('opentype');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: "kern", "liga", "calt";
}

@font-face {
  font-family: 'SF Pro';
  src: url('/assets/fonts/sf-pro/SF-Pro-Text-LightItalic.otf') format('opentype');
  font-weight: 300;
  font-style: italic;
  font-display: swap;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: "kern", "liga", "calt";
}

@font-face {
  font-family: 'SF Pro';
  src: url('/assets/fonts/sf-pro/SF-Pro-Text-Medium.otf') format('opentype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: "kern", "liga", "calt";
}

@font-face {
  font-family: 'SF Pro';
  src: url('/assets/fonts/sf-pro/SF-Pro-Text-MediumItalic.otf') format('opentype');
  font-weight: 500;
  font-style: italic;
  font-display: swap;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: "kern", "liga", "calt";
}

@font-face {
  font-family: 'SF Pro';
  src: url('/assets/fonts/sf-pro/SF-Pro-Text-Bold.otf') format('opentype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: "kern", "liga", "calt";
}

@font-face {
  font-family: 'SF Pro';
  src: url('/assets/fonts/sf-pro/SF-Pro-Text-BoldItalic.otf') format('opentype');
  font-weight: 700;
  font-style: italic;
  font-display: swap;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: "kern", "liga", "calt";
}

@font-face {
  font-family: 'SF Pro';
  src: url('/assets/fonts/sf-pro/SF-Pro-Text-Black.otf') format('opentype');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: "kern", "liga", "calt";
}

@font-face {
  font-family: 'SF Pro';
  src: url('/assets/fonts/sf-pro/SF-Pro-Text-BlackItalic.otf') format('opentype');
  font-weight: 900;
  font-style: italic;
  font-display: swap;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: "kern", "liga", "calt";
}

/* Display variants for headings and larger text */
@font-face {
  font-family: 'SF Pro Display';
  src: url('/assets/fonts/sf-pro/SF-Pro-Display-Regular.otf') format('opentype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: "kern", "liga", "calt";
}

@font-face {
  font-family: 'SF Pro Display';
  src: url('/assets/fonts/sf-pro/SF-Pro-Display-Medium.otf') format('opentype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: "kern", "liga", "calt";
}

@font-face {
  font-family: 'SF Pro Display';
  src: url('/assets/fonts/sf-pro/SF-Pro-Display-Bold.otf') format('opentype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: "kern", "liga", "calt";
}

/* Add a fallback font specifically for Cyrillic characters */
@font-face {
  font-family: 'SF Pro Cyrillic Fallback';
  src: local('Arial'), local('Helvetica');
  unicode-range: U+0400-04FF; /* Cyrillic characters range */
}
