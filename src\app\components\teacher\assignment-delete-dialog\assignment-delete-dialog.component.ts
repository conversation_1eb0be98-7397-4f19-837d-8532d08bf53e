import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";

@Component({
  selector: 'app-assignment-delete-dialog',
  templateUrl: './assignment-delete-dialog.component.html',
  styleUrl: './assignment-delete-dialog.component.css'
})
export class AssignmentDeleteDialogComponent {
  captchaInput: string = '';
  captchaText: string = 'УДАЛИТЬ';

  constructor(
    private dialogRef: MatDialogRef<AssignmentDeleteDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { assignment: any }
  ) {}

  confirm(): void {
    if (this.captchaInput === this.captchaText) {
      this.dialogRef.close(true);
    }
  }

  cancel(): void {
    this.dialogRef.close(false);
  }
}
